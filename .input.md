i want to build to modify my UI so that we have a log out button.

i currently use better Auth to handle the auth. for the UI of the auth related process i use @daveyplate/better-auth-ui. here is some usefull documentation

Follow these steps to set up `@daveyplate/better-auth-ui` in your [Next.js](https://nextjs.org/) project using the **App Router**:

### [AuthUIProvider](https://better-auth-ui.com/integrations/next-js#authuiprovider)

The first step is to set up the [`<AuthUIProvider />`](https://better-auth-ui.com/components/auth-ui-provider) client component with your [`authClient`](https://www.better-auth.com/docs/installation#create-client-instance), wrapping your layout. This is required to provide the context & hooks to your authentication components across your application.

app/providers.tsx

```
"use client"import { AuthUIProvider } from "@daveyplate/better-auth-ui"import Link from "next/link"import { useRouter } from "next/navigation"import type { ReactNode } from "react"import { authClient } from "@/lib/auth-client"export function Providers({ children }: { children: ReactNode }) {    const router = useRouter()    return (        <AuthUIProvider            authClient={authClient}            navigate={router.push}            replace={router.replace}            onSessionChange={() => {                // Clear router cache (protected routes)                router.refresh()            }}            Link={Link}        >            {children}        </AuthUIProvider>    )}
```

**Note**: Since the Next.js **App Router** caches routes by default, navigation to protected routes may fail until you perform a `router.refresh()` to clear the cache. To prevent this issue, you must use `router.refresh()` in the provided `onSessionChange` callback. This forces Next.js to clear the router cache and reload middleware-protected content, ensuring subsequent navigations accurately reflect the current auth state.

Once configured, wrap your layout component with the `Providers` component:

app/layout.tsx

```
import type { ReactNode } from "react"import { Providers } from "./providers"export default function RootLayout({ children }: { children: ReactNode }) {    return (        <html lang="en">            <body>                <Providers>{children}</Providers>            </body>        </html>    )}
```

The [`<AuthUIProvider />`](https://better-auth-ui.com/components/auth-ui-provider) can be fully customized with plugins, styles, localization and more. For more information and all available props, see the [`<AuthUIProvider />`](https://better-auth-ui.com/components/auth-ui-provider) component documentation.

### [Auth Pages](https://better-auth-ui.com/integrations/next-js#auth-pages)

Create a dynamic route segment for authentication views in `app/auth/[authView]/page.tsx`.

app/auth/[path]/page.tsx

```
import { AuthView } from "@daveyplate/better-auth-ui"import { authViewPaths } from "@daveyplate/better-auth-ui/server"export const dynamicParams = falseexport function generateStaticParams() {    return Object.values(authViewPaths).map((path) => ({ path }))}export default async function AuthPage({ params }: { params: Promise<{ path: string }> }) {    const { path } = await params        return (        <main className="container flex grow flex-col items-center justify-center self-center p-4 md:p-6">            <AuthView path={path} />        </main>    )}
```

The newly created dynamic route covers the following paths by default:

- `/auth/sign-in` – Sign in via email/password and social providers
- `/auth/sign-up` – New account registration
- `/auth/magic-link` – Email login without a password
- `/auth/forgot-password` – Trigger email to reset forgotten password
- `/auth/two-factor` – Two-factor authentication
- `/auth/recover-account` – Recover account via backup code
- `/auth/reset-password` – Set new password after receiving reset link
- `/auth/sign-out` – Log the user out of the application
- `/auth/callback` – Internal route to handle Auth callbacks
- `/auth/accept-invitation` – Accept an invitation to an organization

Ensure that any links to the authentication process utilize these routes accordingly. All routes will render the `<AuthView />` component and automatically handle navigation and authentication flow.
